diff --git a/lib/Alembic/CMakeLists.txt b/lib/Alembic/CMakeLists.txt
index 0bd51c91..8e7eae4b 100644
--- a/lib/Alembic/CMakeLists.txt
+++ b/lib/Alembic/CMakeLists.txt
@@ -96,7 +96,7 @@ INSTALL(TARGETS Alembic
 
 set(alembic_targets_file "${PROJECT_NAME}Targets.cmake")
 
-SET(ConfigPackageLocation lib/cmake/Alembic CACHE PATH
+SET(ConfigPackageLocation ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME} CACHE PATH
         "Where to install the Alembic's cmake files")
 
 INCLUDE(CMakePackageConfigHelpers)
